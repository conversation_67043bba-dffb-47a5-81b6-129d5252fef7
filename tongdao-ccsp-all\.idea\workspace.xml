<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="002b0299-09b8-4155-b1cc-fcc8bdc20b11" name="Changes" comment="【update】Docker Swarm API">
      <change afterPath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/modules/swarm/entity/ContainerDeviceResourceRelEntity.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/modules/swarm/entity/DeviceResourceUsageStatsEntity.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/modules/swarm/form/ContainerDeviceResourceRelForm.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/modules/swarm/form/DeviceResourceForm.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/modules/swarm/mapper/ContainerDeviceResourceRelMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/modules/swarm/mapper/DeviceResourceUsageStatsMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/modules/swarm/service/ContainerDeviceResourceService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/modules/swarm/service/impl/ContainerDeviceResourceServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/resources/db/migration/mysql/gmmid1_1_32__add_container_device_resource_rel.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/resources/db/migration/mysql/gmmid1_1_33__fix_device_resource_stats.sql" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tongdao-ccsp-gmmid-console/docker/docker-comose.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/tongdao-ccsp-gmmid-console/docker/docker-comose.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/docker/dcompose-es_dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/docker/dcompose-es_dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/docker/dcompose-es_test.yml" beforeDir="false" afterPath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/docker/dcompose-es_test.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/modules/device/service/VsmService.java" beforeDir="false" afterPath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/modules/device/service/VsmService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/modules/device/service/impl/VsmServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/modules/device/service/impl/VsmServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/modules/swarm/controller/ContainerInstanceController.java" beforeDir="false" afterPath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/modules/swarm/controller/ContainerInstanceController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/modules/swarm/entity/ContainerInstanceEntity.java" beforeDir="false" afterPath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/modules/swarm/entity/ContainerInstanceEntity.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/modules/swarm/form/ContainerDeployForm.java" beforeDir="false" afterPath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/modules/swarm/form/ContainerDeployForm.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/modules/swarm/form/ContainerInstanceForm.java" beforeDir="false" afterPath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/modules/swarm/form/ContainerInstanceForm.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/modules/swarm/mapper/ContainerInstanceRepository.java" beforeDir="false" afterPath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/modules/swarm/mapper/ContainerInstanceRepository.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/modules/swarm/mapper/SwarmNodeRepository.java" beforeDir="false" afterPath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/modules/swarm/mapper/SwarmNodeRepository.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/modules/swarm/service/ApplicationContainerConfigService.java" beforeDir="false" afterPath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/modules/swarm/service/ApplicationContainerConfigService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/modules/swarm/service/impl/ApplicationContainerConfigServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/modules/swarm/service/impl/ApplicationContainerConfigServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/modules/swarm/service/impl/ContainerInstanceServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/modules/swarm/service/impl/ContainerInstanceServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/modules/swarm/service/impl/DockerImageServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/modules/swarm/service/impl/DockerImageServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/modules/swarm/service/impl/SwarmDataAggregationServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/modules/swarm/service/impl/SwarmDataAggregationServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/modules/swarm/service/impl/SwarmNodeServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/modules/swarm/service/impl/SwarmNodeServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/modules/swarm/utils/TraefikConfigHelper.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/utils/DockerSwarmServiceManager.java" beforeDir="false" afterPath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/java/tech/tongdao/ccsp/management/utils/DockerSwarmServiceManager.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/resources/application-dev.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/resources/application-dev.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/resources/application.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/resources/application.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/resources/mapper/swarm/ContainerInstanceMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/resources/mapper/swarm/ContainerInstanceMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/resources/mapper/swarm/SwarmNodeMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/tongdao-ccsp-gmmid-management/src/main/resources/mapper/swarm/SwarmNodeMapper.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CodeInsightWorkspaceSettings">
    <option name="optimizeImportsOnTheFly" value="true" />
  </component>
  <component name="CompilerWorkspaceConfiguration">
    <option name="MAKE_PROJECT_ON_SAVE" value="true" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="JUnit4 Test Class" />
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="PUSH_TAGS">
      <GitPushTagMode>
        <option name="argument" value="--tags" />
        <option name="title" value="All" />
      </GitPushTagMode>
    </option>
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="dev-v3" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="HARD" />
  </component>
  <component name="GitToolBoxStore">
    <option name="projectConfigVersion" value="5" />
    <option name="recentBranches">
      <RecentBranches>
        <option name="branchesForRepo">
          <list>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="广州密码平台" />
                    <option name="lastUsedInstant" value="1755166451" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="dev" />
                    <option name="lastUsedInstant" value="1730687271" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="dev-oper" />
                    <option name="lastUsedInstant" value="1730259871" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="dev-送检" />
                    <option name="lastUsedInstant" value="1714130567" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="feature/zhangjianbo/20231215/base64修改" />
                    <option name="lastUsedInstant" value="1706510667" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$" />
            </RecentBranchesForRepo>
          </list>
        </option>
      </RecentBranches>
    </option>
  </component>
  <component name="InvalidFacetManager">
    <ignored-facets>
      <facet id="tongdao-ccsp-gmmid-management/invalid/JRebel" />
    </ignored-facets>
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\server\apache-maven-3.6.3" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\server\apache-maven-3.6.3\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="2XvipaSWo8hEbgdt4V36M3T3iwf" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="1" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Application.ApiSignatureStressTest.executor": "Run",
    "Application.ApiSignatureUtils.executor": "Run",
    "Application.ConfigLoader.executor": "Run",
    "Application.HMACSM3Utils.executor": "Run",
    "Application.InspectUtils.executor": "Debug",
    "Application.SM2KeyPairGenerator.executor": "Debug",
    "Application.SecurityVsmUtils.executor": "Debug",
    "Application.SimpleDemo.executor": "Run",
    "Application.Test.executor": "Run",
    "Application.TestUtils.executor": "Debug",
    "Application.UserForm.executor": "Debug",
    "HTTP Request.tongdao_ccsp_all-endpoints | #1.executor": "Run",
    "JUnit.SwarmMapperTest.executor": "Debug",
    "Maven.tongdao-ccsp-all [clean].executor": "Run",
    "Maven.tongdao-ccsp-all [compile].executor": "Run",
    "Maven.tongdao-ccsp-all [install].executor": "Run",
    "Maven.tongdao-ccsp-all [package].executor": "Run",
    "Maven.tongdao-ccsp-all [test].executor": "Run",
    "Maven.tongdao-ccsp-all [validate].executor": "Run",
    "Maven.tongdao-ccsp-gmmid-console [clean].executor": "Run",
    "Maven.tongdao-ccsp-gmmid-console [install].executor": "Run",
    "Maven.tongdao-ccsp-gmmid-console [package].executor": "Run",
    "Maven.tongdao-ccsp-gmmid-console [validate].executor": "Run",
    "Maven.tongdao-ccsp-gmmid-management [clean].executor": "Run",
    "Maven.tongdao-ccsp-gmmid-management [package].executor": "Run",
    "Maven.tongdao-ccsp-gmmid-sdk [clean].executor": "Run",
    "Maven.tongdao-ccsp-gmmid-sdk [package].executor": "Run",
    "Maven.tongdao-ccsp-gmmid-sdk [validate].executor": "Run",
    "Notification.DisplayName-DoNotAsk-Database detector": "Database detector",
    "Notification.DisplayName-DoNotAsk-HotSwap": "Hot Swap performed",
    "Notification.DoNotAsk-Database detector": "true",
    "Notification.DoNotAsk-HotSwap": "true",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "ResultTool:SCAN_SERVICE_WITH_LIB": "true",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager.252": "true",
    "RunOnceActivity.git.unshallow": "true",
    "Spring Boot.CcspGmmidConsoleApplication.executor": "Debug",
    "Spring Boot.CcspGmmidManagementApplication.executor": "Debug",
    "Spring Boot.OperationApplication.executor": "JRebel Debug",
    "ToolWindow.Endpoints.ShowToolbar": "false",
    "WebServerToolWindowFactoryState": "false",
    "bean-invoker.enableQuickInvoke": "true",
    "bean-invoker.invokePort": "6653",
    "bean-invoker.isSpringApp": "true",
    "checkBoxType": "false",
    "com.codeium.enabled": "true",
    "com.codeium.snoozedEndTime": "0",
    "com.intellij.testIntegration.createTest.CreateTestDialog.defaultLibrary": "JUnit4",
    "com.intellij.testIntegration.createTest.CreateTestDialog.defaultLibrarySuperClass.JUnit4": "",
    "data.sources.detected.do.not.suggest.again": "true",
    "git-widget-placeholder": "广州密码平台",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/workspace/ccsp_v3/tongdao-ccsp-all/tongdao-ccsp-gmmid-console/config",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Modules",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.2",
    "run.configurations.included.in.services": "true",
    "settings.editor.selected.configurable": "SQL Dialects",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "mysql"
    ]
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RebelAgentSelection">
    <selection>jr</selection>
  </component>
  <component name="RecentsManager">
    <key name="CreateClassDialog.RecentsKey">
      <recent name="tech.tongdao.ccsp.management.modules.device.service" />
      <recent name="tech.tongdao.ccsp.management.modules.faq.service" />
    </key>
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\workspace\ccsp_v3\tongdao-ccsp-all\tongdao-ccsp-gmmid-console\config" />
      <recent name="D:\workspace\tongdao-ccsp-all\tongdao-ccsp-gmmid-sdk\src\test\java\com\ccsp\sdk\utils" />
      <recent name="D:\workspace\tongdao-ccsp-all\tongdao-ccsp-gmmid-management\src\main\java\tech\tongdao\ccsp\management" />
      <recent name="D:\workspace\tongdao-ccsp-all\tongdao-ccsp-gmmid-management\src\main\resources" />
      <recent name="D:\workspace\tongdao-ccsp-all\tongdao-ccsp-gmmid-console\src\main\resources" />
    </key>
    <key name="CreateTestDialog.Recents.Supers">
      <recent name="" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\workspace\tongdao-ccsp-all\tongdao-ccsp-gmmid-operation\src\main\java\tech\tongdao\ccsp\operation\modules\test\mapper\xml" />
      <recent name="D:\workspace\tongdao-ccsp-all\tongdao-ccsp-gmmid-console\lib" />
      <recent name="D:\workspace\tongdao-ccsp-all\tongdao-ccsp-gmmid-console\src\main\resources\mapper" />
    </key>
    <key name="MoveClassesOrPackagesDialog.RECENTS_KEY">
      <recent name="tech.tongdao.ccsp.operation.beans.vo" />
      <recent name="com.ccsp.sdk.utils" />
    </key>
    <key name="CreateTestDialog.RecentsKey">
      <recent name="tech.tongdao.ccsp.management.modules.faq.service.impl" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="tech.tongdao.ccsp.management.modules.swarm.service.impl" />
      <recent name="tech.tongdao.ccsp.operation.beans.vo.mapper" />
      <recent name="tech.tongdao.ccsp.operation.beans.vo" />
      <recent name="tech.tongdao.ccsp.operation.modules.core.controller" />
      <recent name="tech.tongdao.ccsp.management.config" />
    </key>
  </component>
  <component name="RestServicesNavigator">
    <treeState />
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
    <option name="configurationStatuses">
      <map>
        <entry key="SpringBootApplicationConfigurationType">
          <value>
            <map>
              <entry key="CcspGmmidConsoleApplication" value="STOPPED" />
              <entry key="CcspGmmidManagementApplication" value="STOPPED" />
            </map>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.CcspGmmidManagementApplication">
    <configuration default="true" type="docker-deploy" factoryName="docker-compose.yml" temporary="true">
      <deployment type="docker-compose.yml">
        <settings />
      </deployment>
      <method v="2" />
    </configuration>
    <configuration default="true" type="docker-deploy" factoryName="docker-image" temporary="true">
      <deployment type="docker-image">
        <settings />
      </deployment>
      <method v="2" />
    </configuration>
    <configuration default="true" type="docker-deploy" factoryName="dockerfile" temporary="true">
      <deployment type="dockerfile">
        <settings />
      </deployment>
      <method v="2" />
    </configuration>
    <configuration name="SM2KeyPairGenerator" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="tech.tongdao.ccsp.console.SM2KeyPairGenerator" />
      <module name="tongdao-ccsp-gmmid-console" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="tech.tongdao.ccsp.console.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SecurityVsmUtils" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="tech.tongdao.ccsp.management.utils.SecurityVsmUtils" />
      <module name="tongdao-ccsp-gmmid-management" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="tech.tongdao.ccsp.management.utils.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="TestUtils" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="tech.tongdao.ccsp.management.utils.TestUtils" />
      <module name="tongdao-ccsp-gmmid-management" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="tech.tongdao.ccsp.management.utils.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="UserForm" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="tech.tongdao.ccsp.management.modules.user.form.UserForm" />
      <module name="tongdao-ccsp-gmmid-management" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="tech.tongdao.ccsp.management.modules.user.form.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SwarmMapperTest" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="tongdao-ccsp-gmmid-management" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="tech.tongdao.ccsp.management.modules.swarm.test.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="tech.tongdao.ccsp.management.modules.swarm.test" />
      <option name="MAIN_CLASS_NAME" value="tech.tongdao.ccsp.management.modules.swarm.test.SwarmMapperTest" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="JetRunConfigurationType">
      <module name="tongdao-ccsp-gmmid-beans" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="tongdao-ccsp-gmmid-beans" />
      <option name="filePath" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="CcspGmmidConsoleApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="tongdao-ccsp-gmmid-console" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="tech.tongdao.ccsp.console.CcspGmmidConsoleApplication" />
      <option name="VM_PARAMETERS" value="--add-modules  jdk.incubator.foreign --enable-native-access  ALL-UNNAMED" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="CcspGmmidConsoleApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="tongdao-ccsp-gmmid-console" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="tech.tongdao.ccsp.console.CcspGmmidConsoleApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="CcspGmmidManagementApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="ALTERNATIVE_JRE_PATH" value="17-oracle" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
      <module name="tongdao-ccsp-gmmid-management" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="tech.tongdao.ccsp.management.CcspGmmidManagementApplication" />
      <option name="VM_PARAMETERS" value="-Xmx1024m -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=256m -Xss2m" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="CcspGmmidManagementApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="ALTERNATIVE_JRE_PATH" value="17 (2)" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
      <module name="tongdao-ccsp-gmmid-management" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="tech.tongdao.ccsp.management.CcspGmmidManagementApplication" />
      <option name="VM_PARAMETERS" value="-Xmx1024m -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=256m -Xss2m" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="GatewayApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="tongdao-ccsp-gmmid-gateway" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="tech.tongdao.ccsp.gateway.GatewayApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="OperationApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="ALTERNATIVE_JRE_PATH" value="17-oracle" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
      <module name="tongdao-ccsp-gmmid-operation" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="tech.tongdao.ccsp.operation.OperationApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="docker-deploy" factoryName="docker-compose.yml" temporary="true">
      <deployment type="docker-compose.yml">
        <settings />
      </deployment>
      <method v="2" />
    </configuration>
    <configuration default="true" type="docker-deploy" factoryName="docker-image" temporary="true">
      <deployment type="docker-image">
        <settings />
      </deployment>
      <method v="2" />
    </configuration>
    <configuration default="true" type="docker-deploy" factoryName="dockerfile" temporary="true">
      <deployment type="dockerfile">
        <settings />
      </deployment>
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="Application.SM2KeyPairGenerator" />
      <item itemvalue="Application.SecurityVsmUtils" />
      <item itemvalue="Application.TestUtils" />
      <item itemvalue="Application.UserForm" />
      <item itemvalue="JUnit.SwarmMapperTest" />
      <item itemvalue="Spring Boot.CcspGmmidConsoleApplication" />
      <item itemvalue="Spring Boot.CcspGmmidManagementApplication" />
      <item itemvalue="Spring Boot.GatewayApplication" />
      <item itemvalue="Spring Boot.OperationApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="JUnit.SwarmMapperTest" />
        <item itemvalue="Application.UserForm" />
        <item itemvalue="Application.TestUtils" />
        <item itemvalue="Application.SecurityVsmUtils" />
        <item itemvalue="Application.SM2KeyPairGenerator" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-bf35d07a577b-intellij.indexing.shared.core-IU-252.23892.409" />
        <option value="bundled-js-predefined-d6986cc7102b-e03c56caf84a-JavaScript-IU-252.23892.409" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="StructureViewState">
    <option name="selectedTab" value="Physical" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="002b0299-09b8-4155-b1cc-fcc8bdc20b11" name="Changes" comment="" />
      <created>1699516140510</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1699516140510</updated>
      <workItem from="1699516141478" duration="293000" />
      <workItem from="1699516442786" duration="2986000" />
      <workItem from="1699521864641" duration="174000" />
      <workItem from="1699522046695" duration="46000" />
      <workItem from="1699523740507" duration="1576000" />
      <workItem from="1699525362019" duration="540000" />
      <workItem from="1699579580906" duration="5682000" />
      <workItem from="1699605196296" duration="644000" />
      <workItem from="1699606546244" duration="362000" />
      <workItem from="1699608871690" duration="1136000" />
      <workItem from="1699841648373" duration="2406000" />
      <workItem from="1699857105049" duration="347000" />
      <workItem from="1699857463113" duration="2875000" />
      <workItem from="1699860349834" duration="64000" />
      <workItem from="1699860424176" duration="11259000" />
      <workItem from="1699946074204" duration="8416000" />
      <workItem from="1700012794750" duration="3305000" />
      <workItem from="1700028559640" duration="342000" />
      <workItem from="1700030079914" duration="605000" />
      <workItem from="1700034788533" duration="8792000" />
      <workItem from="1700116021994" duration="5011000" />
      <workItem from="1700192804625" duration="900000" />
      <workItem from="1700193733624" duration="713000" />
      <workItem from="1700194834105" duration="143000" />
      <workItem from="1700194979376" duration="124000" />
      <workItem from="1700201121421" duration="155000" />
      <workItem from="1700201362462" duration="344000" />
      <workItem from="1700201743874" duration="330000" />
      <workItem from="1700202085978" duration="4199000" />
      <workItem from="1700206749954" duration="458000" />
      <workItem from="1700207218386" duration="817000" />
      <workItem from="1700216256014" duration="918000" />
      <workItem from="1700532464578" duration="999000" />
      <workItem from="1700710601122" duration="1540000" />
      <workItem from="1701330037428" duration="3158000" />
      <workItem from="1701413958185" duration="8811000" />
      <workItem from="1701426672391" duration="1474000" />
      <workItem from="1701654589464" duration="29975000" />
      <workItem from="1701921036092" duration="241000" />
      <workItem from="1701930364600" duration="21659000" />
      <workItem from="1702259500140" duration="10328000" />
      <workItem from="1702282809155" duration="17905000" />
      <workItem from="1702608751137" duration="1521000" />
      <workItem from="1702622021562" duration="2059000" />
      <workItem from="1702626263145" duration="5341000" />
      <workItem from="1702863793083" duration="17472000" />
      <workItem from="1702952682412" duration="31812000" />
      <workItem from="1703054816801" duration="28471000" />
      <workItem from="1703473183136" duration="17883000" />
      <workItem from="1703559027845" duration="46966000" />
      <workItem from="1704163786091" duration="15274000" />
      <workItem from="1704361991121" duration="7948000" />
      <workItem from="1704704668422" duration="651000" />
      <workItem from="1704962254845" duration="12000" />
      <workItem from="1706077889589" duration="6421000" />
      <workItem from="1706163852964" duration="3510000" />
      <workItem from="1706262251118" duration="25313000" />
      <workItem from="1706523012804" duration="742000" />
      <workItem from="1706581909039" duration="29363000" />
      <workItem from="1706749665259" duration="8000" />
      <workItem from="1706752374973" duration="16121000" />
      <workItem from="1706782372710" duration="3338000" />
      <workItem from="1707013510780" duration="9447000" />
      <workItem from="1708418389444" duration="627000" />
      <workItem from="1708596767407" duration="30000" />
      <workItem from="1709254020206" duration="92000" />
      <workItem from="1709254136116" duration="321000" />
      <workItem from="1710384861566" duration="1687000" />
      <workItem from="1710406197023" duration="611000" />
      <workItem from="1710406824520" duration="4573000" />
      <workItem from="1710468997073" duration="5743000" />
      <workItem from="1710496171226" duration="394000" />
      <workItem from="1710722500250" duration="36436000" />
      <workItem from="1710918523582" duration="4678000" />
      <workItem from="1711005359899" duration="695000" />
      <workItem from="1711014766128" duration="1052000" />
      <workItem from="1711068215662" duration="4744000" />
      <workItem from="1711349434627" duration="1805000" />
      <workItem from="1711357681212" duration="3859000" />
      <workItem from="1711439584141" duration="1047000" />
      <workItem from="1711526828277" duration="59000" />
      <workItem from="1711592433023" duration="1305000" />
      <workItem from="1711607147285" duration="951000" />
      <workItem from="1712053074653" duration="774000" />
      <workItem from="1712114900888" duration="1252000" />
      <workItem from="1712569954938" duration="808000" />
      <workItem from="1712645009727" duration="4991000" />
      <workItem from="1712654269997" duration="4526000" />
      <workItem from="1712713515764" duration="6273000" />
      <workItem from="1712740926300" duration="1446000" />
      <workItem from="1712803020804" duration="3710000" />
      <workItem from="1712903428626" duration="171000" />
      <workItem from="1712910659601" duration="824000" />
      <workItem from="1712911494776" duration="1847000" />
      <workItem from="1713148306597" duration="11107000" />
      <workItem from="1713167547015" duration="2443000" />
      <workItem from="1713170001945" duration="4084000" />
      <workItem from="1713175050766" duration="1681000" />
      <workItem from="1713228663359" duration="21054000" />
      <workItem from="1713317667426" duration="52442000" />
      <workItem from="1713520322129" duration="1472000" />
      <workItem from="1713598677356" duration="133000" />
      <workItem from="1713947112371" duration="2724000" />
      <workItem from="1713954323751" duration="629000" />
      <workItem from="1714009300917" duration="4861000" />
      <workItem from="1714026016657" duration="15413000" />
      <workItem from="1714096202156" duration="19858000" />
      <workItem from="1714265216198" duration="1258000" />
      <workItem from="1714267483339" duration="17490000" />
      <workItem from="1714988373226" duration="3826000" />
      <workItem from="1715045531163" duration="7541000" />
      <workItem from="1715068956737" duration="2736000" />
      <workItem from="1715153415648" duration="5318000" />
      <workItem from="1715238293909" duration="1578000" />
      <workItem from="1715309106586" duration="26713000" />
      <workItem from="1715929830113" duration="1023000" />
      <workItem from="1715931236169" duration="6155000" />
      <workItem from="1716176566148" duration="50000" />
      <workItem from="1716176627208" duration="1243000" />
      <workItem from="1716259509676" duration="1661000" />
      <workItem from="1716886925858" duration="11270000" />
      <workItem from="1717385679300" duration="23000" />
      <workItem from="1717385714059" duration="174000" />
      <workItem from="1717385926215" duration="39000" />
      <workItem from="1717385979702" duration="288000" />
      <workItem from="1717386289488" duration="141000" />
      <workItem from="1717388865519" duration="194000" />
      <workItem from="1717389120777" duration="79000" />
      <workItem from="1717389260934" duration="166000" />
      <workItem from="1717389454272" duration="37000" />
      <workItem from="1723704986711" duration="139000" />
      <workItem from="1723705128354" duration="783000" />
      <workItem from="1723705918062" duration="466000" />
      <workItem from="1723706496051" duration="332000" />
      <workItem from="1723706847377" duration="339000" />
      <workItem from="1723707222237" duration="184000" />
      <workItem from="1723707414319" duration="149000" />
      <workItem from="1723707573201" duration="363000" />
      <workItem from="1723707956916" duration="1080000" />
      <workItem from="1723775685400" duration="3284000" />
      <workItem from="1724035549159" duration="180000" />
      <workItem from="1724035740902" duration="4525000" />
      <workItem from="1724054317048" duration="1037000" />
      <workItem from="1724058089485" duration="872000" />
      <workItem from="1724120276315" duration="59000" />
      <workItem from="1724124264403" duration="4815000" />
      <workItem from="1724139514488" duration="1136000" />
      <workItem from="1724140670269" duration="1386000" />
      <workItem from="1724206549604" duration="1359000" />
      <workItem from="1724207933942" duration="2438000" />
      <workItem from="1724210646147" duration="5315000" />
      <workItem from="1724232850266" duration="10563000" />
      <workItem from="1724640245226" duration="605000" />
      <workItem from="1724640869843" duration="910000" />
      <workItem from="1724641821786" duration="1126000" />
      <workItem from="1724643030537" duration="479000" />
      <workItem from="1724643535645" duration="81000" />
      <workItem from="1724643626699" duration="302000" />
      <workItem from="1724643944580" duration="5449000" />
      <workItem from="1725415842251" duration="1794000" />
      <workItem from="1725588394995" duration="3770000" />
      <workItem from="1725608281370" duration="78000" />
      <workItem from="1725608380162" duration="89000" />
      <workItem from="1725608538731" duration="163000" />
      <workItem from="1725608751081" duration="64000" />
      <workItem from="1725608906958" duration="110000" />
      <workItem from="1725609038312" duration="3125000" />
      <workItem from="1725612666162" duration="1023000" />
      <workItem from="1725849802358" duration="22886000" />
      <workItem from="1725950099423" duration="18889000" />
      <workItem from="1726047453249" duration="5585000" />
      <workItem from="1726114005490" duration="126000" />
      <workItem from="1726114138264" duration="12798000" />
      <workItem from="1726194632484" duration="2042000" />
      <workItem from="1726197025293" duration="28829000" />
      <workItem from="1726625102462" duration="3574000" />
      <workItem from="1726634112236" duration="3459000" />
      <workItem from="1726641664296" duration="3162000" />
      <workItem from="1726644838671" duration="9309000" />
      <workItem from="1726720534930" duration="317000" />
      <workItem from="1726720870751" duration="203000" />
      <workItem from="1726721155717" duration="84000" />
      <workItem from="1726721246730" duration="1061000" />
      <workItem from="1726726491408" duration="4475000" />
      <workItem from="1726731724941" duration="34000" />
      <workItem from="1726731765948" duration="119000" />
      <workItem from="1726731941789" duration="524000" />
      <workItem from="1726798039393" duration="18290000" />
      <workItem from="1726833015749" duration="23651000" />
      <workItem from="1726975795799" duration="12611000" />
      <workItem from="1727057575781" duration="21351000" />
      <workItem from="1727147566168" duration="11652000" />
      <workItem from="1727230787689" duration="14322000" />
      <workItem from="1727312079212" duration="5005000" />
      <workItem from="1727328819384" duration="739000" />
      <workItem from="1727404385034" duration="173000" />
      <workItem from="1727404567673" duration="98000" />
      <workItem from="1727404676644" duration="197000" />
      <workItem from="1727404895129" duration="180000" />
      <workItem from="1727405125940" duration="905000" />
      <workItem from="1727409083950" duration="115000" />
      <workItem from="1727409210925" duration="2410000" />
      <workItem from="1728441794460" duration="4199000" />
      <workItem from="1728717055470" duration="2248000" />
      <workItem from="1728719669523" duration="1173000" />
      <workItem from="1728720852915" duration="2863000" />
      <workItem from="1728874023212" duration="30772000" />
      <workItem from="1729138737934" duration="952000" />
      <workItem from="1729763889515" duration="3644000" />
      <workItem from="1730096956559" duration="9000" />
      <workItem from="1730259754967" duration="1995000" />
      <workItem from="1730273361696" duration="27000" />
      <workItem from="1730273455994" duration="8000" />
      <workItem from="1730275243912" duration="1940000" />
      <workItem from="1730281849277" duration="430000" />
      <workItem from="1730282409794" duration="388000" />
      <workItem from="1730444539306" duration="1709000" />
      <workItem from="1730447626187" duration="1082000" />
      <workItem from="1730687126582" duration="1810000" />
      <workItem from="1730863220594" duration="1395000" />
      <workItem from="1730873595575" duration="679000" />
      <workItem from="1730885208203" duration="6000" />
      <workItem from="1731407910253" duration="12456000" />
      <workItem from="1749099571698" duration="3225000" />
      <workItem from="1749118339162" duration="2043000" />
      <workItem from="1749120464336" duration="354000" />
      <workItem from="1749121111362" duration="8000" />
      <workItem from="1749121328023" duration="24000" />
      <workItem from="1749198631623" duration="2155000" />
      <workItem from="1749206879150" duration="1281000" />
      <workItem from="1749208656561" duration="508000" />
      <workItem from="1749456386489" duration="542000" />
      <workItem from="1749459389542" duration="23000" />
      <workItem from="1749459425977" duration="3243000" />
      <workItem from="1749517233686" duration="3472000" />
      <workItem from="1750232359924" duration="448000" />
      <workItem from="1753434470368" duration="1895000" />
      <workItem from="1753437114070" duration="121000" />
      <workItem from="1753437255083" duration="847000" />
      <workItem from="1755165785521" duration="694000" />
      <workItem from="1755252346494" duration="98000" />
      <workItem from="1755253848259" duration="2435000" />
      <workItem from="1755497494110" duration="2504000" />
      <workItem from="1755500178946" duration="1850000" />
      <workItem from="1755503536578" duration="1144000" />
      <workItem from="1755504910694" duration="84000" />
      <workItem from="1755506030522" duration="440000" />
      <workItem from="1755506647528" duration="5368000" />
      <workItem from="1755594091421" duration="7359000" />
      <workItem from="1755659902748" duration="7949000" />
      <workItem from="1755674061582" duration="5572000" />
      <workItem from="1755747774196" duration="426000" />
      <workItem from="1755757055210" duration="86000" />
      <workItem from="1755758088284" duration="888000" />
      <workItem from="1755765101033" duration="334000" />
      <workItem from="1755766290279" duration="1581000" />
      <workItem from="1755768562431" duration="1791000" />
      <workItem from="1756105860404" duration="1119000" />
      <workItem from="1756110697514" duration="4426000" />
      <workItem from="1756116069176" duration="404000" />
      <workItem from="1756176791157" duration="9714000" />
      <workItem from="1756437149770" duration="2194000" />
      <workItem from="1756451633694" duration="11872000" />
      <workItem from="1756709922819" duration="5908000" />
      <workItem from="1756718492127" duration="6177000" />
      <workItem from="1756783641659" duration="2525000" />
      <workItem from="1756798105805" duration="13483000" />
      <workItem from="1756889982651" duration="10525000" />
      <workItem from="1757053832827" duration="1230000" />
      <workItem from="1757055707269" duration="568000" />
    </task>
    <task id="LOCAL-00026" summary="【update】日志微调">
      <option name="closed" value="true" />
      <created>1710835651658</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1710835651658</updated>
    </task>
    <task id="LOCAL-00027" summary="【update】完善SDK身份验证">
      <option name="closed" value="true" />
      <created>1710901047912</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1710901047912</updated>
    </task>
    <task id="LOCAL-00028" summary="【update】更新引擎版本">
      <option name="closed" value="true" />
      <created>1711015504044</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1711015504044</updated>
    </task>
    <task id="LOCAL-00029" summary="【update】sk sm9签名验证">
      <option name="closed" value="true" />
      <created>1713176339817</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1713176339818</updated>
    </task>
    <task id="LOCAL-00030" summary="【update】应用密钥sm9签名验证">
      <option name="closed" value="true" />
      <created>1713237499025</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1713237499025</updated>
    </task>
    <task id="LOCAL-00031" summary="【update】账号手机号SM1加密验签">
      <option name="closed" value="true" />
      <created>1713250186473</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1713250186473</updated>
    </task>
    <task id="LOCAL-00032" summary="【update】sm1、sm9相关修改">
      <option name="closed" value="true" />
      <created>1713345269812</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1713345269812</updated>
    </task>
    <task id="LOCAL-00033" summary="【update】sm1、sm9相关修改，外置服务">
      <option name="closed" value="true" />
      <created>1713488705442</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1713488705442</updated>
    </task>
    <task id="LOCAL-00034" summary="【update】微调">
      <option name="closed" value="true" />
      <created>1713492364508</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1713492364508</updated>
    </task>
    <task id="LOCAL-00035" summary="【update】配置文件更新">
      <option name="closed" value="true" />
      <created>1713514107468</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1713514107468</updated>
    </task>
    <task id="LOCAL-00036" summary="【update】ukey登录修复">
      <option name="closed" value="true" />
      <created>1713598703019</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1713598703019</updated>
    </task>
    <task id="LOCAL-00037" summary="【update】送检版本需求">
      <option name="closed" value="true" />
      <created>1714043960490</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1714043960490</updated>
    </task>
    <task id="LOCAL-00038" summary="【update】送检版本需求">
      <option name="closed" value="true" />
      <created>1714130300705</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1714130300705</updated>
    </task>
    <task id="LOCAL-00039" summary="【update】送检版本需求">
      <option name="closed" value="true" />
      <created>1714288270064</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1714288270064</updated>
    </task>
    <task id="LOCAL-00040" summary="【update】送检版本需求">
      <option name="closed" value="true" />
      <created>1714457899026</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1714457899026</updated>
    </task>
    <task id="LOCAL-00041" summary="【update】送检版本需求">
      <option name="closed" value="true" />
      <created>1715074025237</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1715074025238</updated>
    </task>
    <task id="LOCAL-00042" summary="【update】送检版本需求">
      <option name="closed" value="true" />
      <created>1715389339684</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1715389339684</updated>
    </task>
    <task id="LOCAL-00043" summary="【update】奇奇怪怪的bug">
      <option name="closed" value="true" />
      <created>1725588966982</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>1725588966982</updated>
    </task>
    <task id="LOCAL-00044" summary="【update】接口调整">
      <option name="closed" value="true" />
      <created>1726121948390</created>
      <option name="number" value="00044" />
      <option name="presentableId" value="LOCAL-00044" />
      <option name="project" value="LOCAL" />
      <updated>1726121948390</updated>
    </task>
    <task id="LOCAL-00045" summary="【update】修改controller基础类的api路径">
      <option name="closed" value="true" />
      <created>1726123606073</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>1726123606073</updated>
    </task>
    <task id="LOCAL-00046" summary="【update】@Controller -&gt; @RestController">
      <option name="closed" value="true" />
      <created>1726127284611</created>
      <option name="number" value="00046" />
      <option name="presentableId" value="LOCAL-00046" />
      <option name="project" value="LOCAL" />
      <updated>1726127284611</updated>
    </task>
    <task id="LOCAL-00047" summary="【update】list-page分页bug修复">
      <option name="closed" value="true" />
      <created>1726133014776</created>
      <option name="number" value="00047" />
      <option name="presentableId" value="LOCAL-00047" />
      <option name="project" value="LOCAL" />
      <updated>1726133014776</updated>
    </task>
    <task id="LOCAL-00048" summary="【update】修改baseController，添加返回对象的修改方法">
      <option name="closed" value="true" />
      <created>1726197354737</created>
      <option name="number" value="00048" />
      <option name="presentableId" value="LOCAL-00048" />
      <option name="project" value="LOCAL" />
      <updated>1726197354737</updated>
    </task>
    <task id="LOCAL-00049" summary="【update】运营中心-设备管理">
      <option name="closed" value="true" />
      <created>1726209718722</created>
      <option name="number" value="00049" />
      <option name="presentableId" value="LOCAL-00049" />
      <option name="project" value="LOCAL" />
      <updated>1726209718722</updated>
    </task>
    <task id="LOCAL-00050" summary="【update】新增厂家、ISP、日志表">
      <option name="closed" value="true" />
      <created>1726216987801</created>
      <option name="number" value="00050" />
      <option name="presentableId" value="LOCAL-00050" />
      <option name="project" value="LOCAL" />
      <updated>1726216987801</updated>
    </task>
    <task id="LOCAL-00051" summary="【update】操作日志表">
      <option name="closed" value="true" />
      <created>1726219172293</created>
      <option name="number" value="00051" />
      <option name="presentableId" value="LOCAL-00051" />
      <option name="project" value="LOCAL" />
      <updated>1726219172293</updated>
    </task>
    <task id="LOCAL-00052" summary="【update】实体微调">
      <option name="closed" value="true" />
      <created>1726220230890</created>
      <option name="number" value="00052" />
      <option name="presentableId" value="LOCAL-00052" />
      <option name="project" value="LOCAL" />
      <updated>1726220230890</updated>
    </task>
    <task id="LOCAL-00053" summary="【update】调整BaseController">
      <option name="closed" value="true" />
      <created>1726299155307</created>
      <option name="number" value="00053" />
      <option name="presentableId" value="LOCAL-00053" />
      <option name="project" value="LOCAL" />
      <updated>1726299155307</updated>
    </task>
    <task id="LOCAL-00054" summary="【update】云密码机实体微调，添加ISP相关表信息">
      <option name="closed" value="true" />
      <created>1726645020501</created>
      <option name="number" value="00054" />
      <option name="presentableId" value="LOCAL-00054" />
      <option name="project" value="LOCAL" />
      <updated>1726645020501</updated>
    </task>
    <task id="LOCAL-00055" summary="【update】ISP机房信息">
      <option name="closed" value="true" />
      <created>1726715648569</created>
      <option name="number" value="00055" />
      <option name="presentableId" value="LOCAL-00055" />
      <option name="project" value="LOCAL" />
      <updated>1726715648569</updated>
    </task>
    <task id="LOCAL-00056" summary="CustomLongSerializer微调">
      <option name="closed" value="true" />
      <created>1726729462994</created>
      <option name="number" value="00056" />
      <option name="presentableId" value="LOCAL-00056" />
      <option name="project" value="LOCAL" />
      <updated>1726729462995</updated>
    </task>
    <task id="LOCAL-00057" summary="列表接口微调">
      <option name="closed" value="true" />
      <created>1726887567656</created>
      <option name="number" value="00057" />
      <option name="presentableId" value="LOCAL-00057" />
      <option name="project" value="LOCAL" />
      <updated>1726887567656</updated>
    </task>
    <task id="LOCAL-00058" summary="列表接口微调">
      <option name="closed" value="true" />
      <created>1726911224990</created>
      <option name="number" value="00058" />
      <option name="presentableId" value="LOCAL-00058" />
      <option name="project" value="LOCAL" />
      <updated>1726911224990</updated>
    </task>
    <task id="LOCAL-00059" summary="列表接口微调">
      <option name="closed" value="true" />
      <created>1727073646682</created>
      <option name="number" value="00059" />
      <option name="presentableId" value="LOCAL-00059" />
      <option name="project" value="LOCAL" />
      <updated>1727073646683</updated>
    </task>
    <task id="LOCAL-00060" summary="列表接口微调">
      <option name="closed" value="true" />
      <created>1727158483701</created>
      <option name="number" value="00060" />
      <option name="presentableId" value="LOCAL-00060" />
      <option name="project" value="LOCAL" />
      <updated>1727158483701</updated>
    </task>
    <task id="LOCAL-00061" summary="列表接口微调">
      <option name="closed" value="true" />
      <created>1727234008233</created>
      <option name="number" value="00061" />
      <option name="presentableId" value="LOCAL-00061" />
      <option name="project" value="LOCAL" />
      <updated>1727234008233</updated>
    </task>
    <task id="LOCAL-00062" summary="密评列表">
      <option name="closed" value="true" />
      <created>1727266190774</created>
      <option name="number" value="00062" />
      <option name="presentableId" value="LOCAL-00062" />
      <option name="project" value="LOCAL" />
      <updated>1727266190774</updated>
    </task>
    <task id="LOCAL-00063" summary="删除了`DyServiceImpl.java`文件中的`@ConditionalOnProperty`注解和相应的`enabled`属性，改为在`@Value`注解中给出默认值`http://127.0.0.1`。同时，删除了`application-dev.yaml`中的`dy.enabled`属性。这一更改使服务在默认情况下始终启用，并提供了一个默认的URI配置。">
      <option name="closed" value="true" />
      <created>1728892020195</created>
      <option name="number" value="00063" />
      <option name="presentableId" value="LOCAL-00063" />
      <option name="project" value="LOCAL" />
      <updated>1728892020196</updated>
    </task>
    <task id="LOCAL-00064" summary="启用了之前被注释掉的连接池配置，将连接池的最大总连接数设置为5000，并将每个路由的默认最大连接数从2500修改为500。此更改旨在优化HttpClient的连接管理，提升性能和资源利用效率。">
      <option name="closed" value="true" />
      <created>1730687361496</created>
      <option name="number" value="00064" />
      <option name="presentableId" value="LOCAL-00064" />
      <option name="project" value="LOCAL" />
      <updated>1730687361496</updated>
    </task>
    <task id="LOCAL-00065" summary="sdk连接池微调">
      <option name="closed" value="true" />
      <created>1730874266070</created>
      <option name="number" value="00065" />
      <option name="presentableId" value="LOCAL-00065" />
      <option name="project" value="LOCAL" />
      <updated>1730874266070</updated>
    </task>
    <task id="LOCAL-00066" summary="【update】增加cmac忽略字段">
      <option name="closed" value="true" />
      <created>1753437188469</created>
      <option name="number" value="00066" />
      <option name="presentableId" value="LOCAL-00066" />
      <option name="project" value="LOCAL" />
      <updated>1753437188470</updated>
    </task>
    <task id="LOCAL-00067" summary="【update】接入docker swarm，未完成">
      <option name="closed" value="true" />
      <created>1755253938183</created>
      <option name="number" value="00067" />
      <option name="presentableId" value="LOCAL-00067" />
      <option name="project" value="LOCAL" />
      <updated>1755253938183</updated>
    </task>
    <task id="LOCAL-00068" summary="【update】/project/node/v1/infos接口">
      <option name="closed" value="true" />
      <created>1755508774830</created>
      <option name="number" value="00068" />
      <option name="presentableId" value="LOCAL-00068" />
      <option name="project" value="LOCAL" />
      <updated>1755508774830</updated>
    </task>
    <task id="LOCAL-00069" summary="【update】完善/project/node/v1/infos接口">
      <option name="closed" value="true" />
      <created>1755510674866</created>
      <option name="number" value="00069" />
      <option name="presentableId" value="LOCAL-00069" />
      <option name="project" value="LOCAL" />
      <updated>1755510674866</updated>
    </task>
    <task id="LOCAL-00070" summary="【update】完善/project/node/v1/infos接口">
      <option name="closed" value="true" />
      <created>1755512217144</created>
      <option name="number" value="00070" />
      <option name="presentableId" value="LOCAL-00070" />
      <option name="project" value="LOCAL" />
      <updated>1755512217144</updated>
    </task>
    <task id="LOCAL-00071" summary="【update】Docker Swarm完善">
      <option name="closed" value="true" />
      <created>1755594432107</created>
      <option name="number" value="00071" />
      <option name="presentableId" value="LOCAL-00071" />
      <option name="project" value="LOCAL" />
      <updated>1755594432107</updated>
    </task>
    <task id="LOCAL-00072" summary="【update】Docker Swarm完善">
      <option name="closed" value="true" />
      <created>1755679087334</created>
      <option name="number" value="00072" />
      <option name="presentableId" value="LOCAL-00072" />
      <option name="project" value="LOCAL" />
      <updated>1755679087334</updated>
    </task>
    <task id="LOCAL-00073" summary="【update】Docker Swarm API">
      <option name="closed" value="true" />
      <created>1755758452778</created>
      <option name="number" value="00073" />
      <option name="presentableId" value="LOCAL-00073" />
      <option name="project" value="LOCAL" />
      <updated>1755758452778</updated>
    </task>
    <task id="LOCAL-00074" summary="【update】Docker Swarm API">
      <option name="closed" value="true" />
      <created>1756453395240</created>
      <option name="number" value="00074" />
      <option name="presentableId" value="LOCAL-00074" />
      <option name="project" value="LOCAL" />
      <updated>1756453395240</updated>
    </task>
    <option name="localTasksCounter" value="75" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
        <option value="GitHub.CommitStatus" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="OPEN_GENERIC_TABS">
      <map>
        <entry key="9901ba69-5cef-411d-86ad-6993069a247f" value="TOOL_WINDOW" />
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="9901ba69-5cef-411d-86ad-6993069a247f">
          <value>
            <State />
          </value>
        </entry>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="调整了HttpClientUtils, ConsoleProfile和SDFFactory以支持动态连接池配置。首先，在HttpClientUtils中，连接管理器的最大连接数和每个路由的最大连接数不再是硬编码的常量，而是动态计算的值，基于ConsoleProfile中的poolSize。通过日志记录增加了对这些值的调试输出。其次，在ConsoleProfile中引入了一个静态变量poolSize，并提供了相应的getter和setter方法，以管理该变量。最后，SDFFactory的handleConsoleProfile方法现在接受一个额外的参数poolSize，并将此值设置到ConsoleProfile中。这些更改使得连接池的大小" />
    <MESSAGE value="调整了HttpClientUtils, ConsoleProfile和SDFFactory以支持动态连接池配置。首先，在HttpClientUtils中，连接管理器的最大连接数和每个路由的最大连接数不再是硬编码的常量，而是动态计算的值，基于ConsoleProfile中的poolSize。通过日志记录增加了对这些值的调试输出。其次，在ConsoleProfile中引入了一个静态变量poolSize，并提供了相应的getter和setter方法，以管理该变量。最后，SDFFactory的handleConsoleProfile方法现在接受一个额外的参数poolSize，并将此值设置到ConsoleProfile中。这些更改使得连接池的大小可以" />
    <MESSAGE value="调整了HttpClientUtils, ConsoleProfile和SDFFactory以支持动态连接池配置。首先，在HttpClientUtils中，连接管理器的最大连接数和每个路由的最大连接数不再是硬编码的常量，而是动态计算的值，基于ConsoleProfile中的poolSize。通过日志记录增加了对这些值的调试输出。其次，在ConsoleProfile中引入了一个静态变量poolSize，并提供了相应的getter和setter方法，以管理该变量。最后，SDFFactory的handleConsoleProfile方法现在接受一个额外的参数poolSize，并将此值设置到ConsoleProfile中。这些更改使得连接池的大小可以根据" />
    <MESSAGE value="调整了HttpClientUtils, ConsoleProfile和SDFFactory以支持动态连接池配置。首先，在HttpClientUtils中，连接管理器的最大连接数和每个路由的最大连接数不再是硬编码的常量，而是动态计算的值，基于ConsoleProfile中的poolSize。通过日志记录增加了对这些值的调试输出。其次，在ConsoleProfile中引入了一个静态变量poolSize，并提供了相应的getter和setter方法，以管理该变量。最后，SDFFactory的handleConsoleProfile方法现在接受一个额外的参数poolSize，并将此值设置到ConsoleProfile中。这些更改使得连接池的大小可以根据需要" />
    <MESSAGE value="调整了HttpClientUtils, ConsoleProfile和SDFFactory以支持动态连接池配置。首先，在HttpClientUtils中，连接管理器的最大连接数和每个路由的最大连接数不再是硬编码的常量，而是动态计算的值，基于ConsoleProfile中的poolSize。通过日志记录增加了对这些值的调试输出。其次，在ConsoleProfile中引入了一个静态变量poolSize，并提供了相应的getter和setter方法，以管理该变量。最后，SDFFactory的handleConsoleProfile方法现在接受一个额外的参数poolSize，并将此值设置到ConsoleProfile中。这些更改使得连接池的大小可以根据需要进行" />
    <MESSAGE value="调整了HttpClientUtils, ConsoleProfile和SDFFactory以支持动态连接池配置。首先，在HttpClientUtils中，连接管理器的最大连接数和每个路由的最大连接数不再是硬编码的常量，而是动态计算的值，基于ConsoleProfile中的poolSize。通过日志记录增加了对这些值的调试输出。其次，在ConsoleProfile中引入了一个静态变量poolSize，并提供了相应的getter和setter方法，以管理该变量。最后，SDFFactory的handleConsoleProfile方法现在接受一个额外的参数poolSize，并将此值设置到ConsoleProfile中。这些更改使得连接池的大小可以根据需要进行配置" />
    <MESSAGE value="调整了HttpClientUtils, ConsoleProfile和SDFFactory以支持动态连接池配置。首先，在HttpClientUtils中，连接管理器的最大连接数和每个路由的最大连接数不再是硬编码的常量，而是动态计算的值，基于ConsoleProfile中的poolSize。通过日志记录增加了对这些值的调试输出。其次，在ConsoleProfile中引入了一个静态变量poolSize，并提供了相应的getter和setter方法，以管理该变量。最后，SDFFactory的handleConsoleProfile方法现在接受一个额外的参数poolSize，并将此值设置到ConsoleProfile中。这些更改使得连接池的大小可以根据需要进行配置，提高" />
    <MESSAGE value="调整了HttpClientUtils, ConsoleProfile和SDFFactory以支持动态连接池配置。首先，在HttpClientUtils中，连接管理器的最大连接数和每个路由的最大连接数不再是硬编码的常量，而是动态计算的值，基于ConsoleProfile中的poolSize。通过日志记录增加了对这些值的调试输出。其次，在ConsoleProfile中引入了一个静态变量poolSize，并提供了相应的getter和setter方法，以管理该变量。最后，SDFFactory的handleConsoleProfile方法现在接受一个额外的参数poolSize，并将此值设置到ConsoleProfile中。这些更改使得连接池的大小可以根据需要进行配置，提高了" />
    <MESSAGE value="调整了HttpClientUtils, ConsoleProfile和SDFFactory以支持动态连接池配置。首先，在HttpClientUtils中，连接管理器的最大连接数和每个路由的最大连接数不再是硬编码的常量，而是动态计算的值，基于ConsoleProfile中的poolSize。通过日志记录增加了对这些值的调试输出。其次，在ConsoleProfile中引入了一个静态变量poolSize，并提供了相应的getter和setter方法，以管理该变量。最后，SDFFactory的handleConsoleProfile方法现在接受一个额外的参数poolSize，并将此值设置到ConsoleProfile中。这些更改使得连接池的大小可以根据需要进行配置，提高了系统" />
    <MESSAGE value="调整了HttpClientUtils, ConsoleProfile和SDFFactory以支持动态连接池配置。首先，在HttpClientUtils中，连接管理器的最大连接数和每个路由的最大连接数不再是硬编码的常量，而是动态计算的值，基于ConsoleProfile中的poolSize。通过日志记录增加了对这些值的调试输出。其次，在ConsoleProfile中引入了一个静态变量poolSize，并提供了相应的getter和setter方法，以管理该变量。最后，SDFFactory的handleConsoleProfile方法现在接受一个额外的参数poolSize，并将此值设置到ConsoleProfile中。这些更改使得连接池的大小可以根据需要进行配置，提高了系统的" />
    <MESSAGE value="调整了HttpClientUtils, ConsoleProfile和SDFFactory以支持动态连接池配置。首先，在HttpClientUtils中，连接管理器的最大连接数和每个路由的最大连接数不再是硬编码的常量，而是动态计算的值，基于ConsoleProfile中的poolSize。通过日志记录增加了对这些值的调试输出。其次，在ConsoleProfile中引入了一个静态变量poolSize，并提供了相应的getter和setter方法，以管理该变量。最后，SDFFactory的handleConsoleProfile方法现在接受一个额外的参数poolSize，并将此值设置到ConsoleProfile中。这些更改使得连接池的大小可以根据需要进行配置，提高了系统的灵" />
    <MESSAGE value="调整了HttpClientUtils, ConsoleProfile和SDFFactory以支持动态连接池配置。首先，在HttpClientUtils中，连接管理器的最大连接数和每个路由的最大连接数不再是硬编码的常量，而是动态计算的值，基于ConsoleProfile中的poolSize。通过日志记录增加了对这些值的调试输出。其次，在ConsoleProfile中引入了一个静态变量poolSize，并提供了相应的getter和setter方法，以管理该变量。最后，SDFFactory的handleConsoleProfile方法现在接受一个额外的参数poolSize，并将此值设置到ConsoleProfile中。这些更改使得连接池的大小可以根据需要进行配置，提高了系统的灵活" />
    <MESSAGE value="调整了HttpClientUtils, ConsoleProfile和SDFFactory以支持动态连接池配置。首先，在HttpClientUtils中，连接管理器的最大连接数和每个路由的最大连接数不再是硬编码的常量，而是动态计算的值，基于ConsoleProfile中的poolSize。通过日志记录增加了对这些值的调试输出。其次，在ConsoleProfile中引入了一个静态变量poolSize，并提供了相应的getter和setter方法，以管理该变量。最后，SDFFactory的handleConsoleProfile方法现在接受一个额外的参数poolSize，并将此值设置到ConsoleProfile中。这些更改使得连接池的大小可以根据需要进行配置，提高了系统的灵活性" />
    <MESSAGE value="调整了HttpClientUtils, ConsoleProfile和SDFFactory以支持动态连接池配置。首先，在HttpClientUtils中，连接管理器的最大连接数和每个路由的最大连接数不再是硬编码的常量，而是动态计算的值，基于ConsoleProfile中的poolSize。通过日志记录增加了对这些值的调试输出。其次，在ConsoleProfile中引入了一个静态变量poolSize，并提供了相应的getter和setter方法，以管理该变量。最后，SDFFactory的handleConsoleProfile方法现在接受一个额外的参数poolSize，并将此值设置到ConsoleProfile中。这些更改使得连接池的大小可以根据需要进行配置，提高了系统的灵活性和" />
    <MESSAGE value="调整了HttpClientUtils, ConsoleProfile和SDFFactory以支持动态连接池配置。首先，在HttpClientUtils中，连接管理器的最大连接数和每个路由的最大连接数不再是硬编码的常量，而是动态计算的值，基于ConsoleProfile中的poolSize。通过日志记录增加了对这些值的调试输出。其次，在ConsoleProfile中引入了一个静态变量poolSize，并提供了相应的getter和setter方法，以管理该变量。最后，SDFFactory的handleConsoleProfile方法现在接受一个额外的参数poolSize，并将此值设置到ConsoleProfile中。这些更改使得连接池的大小可以根据需要进行配置，提高了系统的灵活性和可" />
    <MESSAGE value="调整了HttpClientUtils, ConsoleProfile和SDFFactory以支持动态连接池配置。首先，在HttpClientUtils中，连接管理器的最大连接数和每个路由的最大连接数不再是硬编码的常量，而是动态计算的值，基于ConsoleProfile中的poolSize。通过日志记录增加了对这些值的调试输出。其次，在ConsoleProfile中引入了一个静态变量poolSize，并提供了相应的getter和setter方法，以管理该变量。最后，SDFFactory的handleConsoleProfile方法现在接受一个额外的参数poolSize，并将此值设置到ConsoleProfile中。这些更改使得连接池的大小可以根据需要进行配置，提高了系统的灵活性和可维护" />
    <MESSAGE value="调整了HttpClientUtils, ConsoleProfile和SDFFactory以支持动态连接池配置。首先，在HttpClientUtils中，连接管理器的最大连接数和每个路由的最大连接数不再是硬编码的常量，而是动态计算的值，基于ConsoleProfile中的poolSize。通过日志记录增加了对这些值的调试输出。其次，在ConsoleProfile中引入了一个静态变量poolSize，并提供了相应的getter和setter方法，以管理该变量。最后，SDFFactory的handleConsoleProfile方法现在接受一个额外的参数poolSize，并将此值设置到ConsoleProfile中。这些更改使得连接池的大小可以根据需要进行配置，提高了系统的灵活性和可维护性" />
    <MESSAGE value="调整了HttpClientUtils, ConsoleProfile和SDFFactory以支持动态连接池配置。首先，在HttpClientUtils中，连接管理器的最大连接数和每个路由的最大连接数不再是硬编码的常量，而是动态计算的值，基于ConsoleProfile中的poolSize。通过日志记录增加了对这些值的调试输出。其次，在ConsoleProfile中引入了一个静态变量poolSize，并提供了相应的getter和setter方法，以管理该变量。最后，SDFFactory的handleConsoleProfile方法现在接受一个额外的参数poolSize，并将此值设置到ConsoleProfile中。这些更改使得连接池的大小可以根据需要进行配置，提高了系统的灵活性和可维护性。" />
    <MESSAGE value="sdk连接池微调" />
    <MESSAGE value="【update】增加cmac忽略字段" />
    <MESSAGE value="【update】接入docker swarm，未完成" />
    <MESSAGE value="【update】/project/node/v1/infos接口" />
    <MESSAGE value="【update】完善/project/node/v1/infos接口" />
    <MESSAGE value="【update】Docker Swarm完善" />
    <MESSAGE value="【update】Docker Swarm API" />
    <option name="LAST_COMMIT_MESSAGE" value="【update】Docker Swarm API" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>