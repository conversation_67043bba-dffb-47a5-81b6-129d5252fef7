package tech.tongdao.ccsp.management.modules.swarm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ccsp.gmmid.beans.exceptions.BusinessException;
import com.ccsp.gmmid.beans.form.Pager;
import com.github.dockerjava.api.DockerClient;
import com.github.dockerjava.api.model.Image;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tongdao.ccsp.management.modules.swarm.converter.SwarmEntityMapper;
import tech.tongdao.ccsp.management.modules.swarm.entity.DockerImageEntity;
import tech.tongdao.ccsp.management.modules.swarm.form.DockerImageForm;
import tech.tongdao.ccsp.management.modules.swarm.mapper.ContainerInstanceRepository;
import tech.tongdao.ccsp.management.modules.swarm.mapper.DockerImageRepository;
import tech.tongdao.ccsp.management.modules.swarm.service.DockerImageService;
import tech.tongdao.ccsp.management.utils.DockerSwarmServiceManager;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Docker镜像管理服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Service
@CacheConfig(cacheNames = {"docker-image-cache"})
public class DockerImageServiceImpl extends ServiceImpl<DockerImageRepository, DockerImageEntity> implements DockerImageService {

    private static final Logger LOGGER = LoggerFactory.getLogger(DockerImageServiceImpl.class);
    public static final String CCSP_CONSOLE = "ccsp_console";

    @Autowired
    private DockerImageRepository dockerImageRepository;

    @Autowired
    private SwarmEntityMapper swarmEntityMapper;

    @Autowired
    private ContainerInstanceRepository containerInstanceRepository;

    @Autowired
    private DockerSwarmServiceManager dockerSwarmServiceManager;

    @Override
    public Pager<DockerImageForm> listPage(DockerImageForm form) {
        try {
            LOGGER.info("分页查询Docker镜像列表，查询条件：{}", form);

            // 使用MyBatis-Plus Lambda查询构建查询条件
            LambdaQueryWrapper<DockerImageEntity> wrapper = new LambdaQueryWrapper<>();

            if (StringUtils.isNotBlank(form.getName())) {
                wrapper.like(DockerImageEntity::getName, form.getName());
            }
            if (StringUtils.isNotBlank(form.getTag())) {
                wrapper.like(DockerImageEntity::getTag, form.getTag());
            }
            if (StringUtils.isNotBlank(form.getStatus())) {
                wrapper.eq(DockerImageEntity::getStatus, form.getStatus());
            }

            // 添加排序
            wrapper.orderByDesc(DockerImageEntity::getCreateTime);

            // 分页查询
            Page<DockerImageEntity> page = new Page<>(form.getPage(), form.getPageSize());
            IPage<DockerImageEntity> result = this.page(page, wrapper);

            // 使用MapStruct转换Entity到Form
            List<DockerImageForm> formList = swarmEntityMapper.toDockerImageFormList(result.getRecords());

            LOGGER.info("查询到{}条Docker镜像记录", result.getTotal());
            return new Pager<>(formList, (int) result.getTotal(), form.getPage(), form.getPageSize());

        } catch (Exception e) {
            LOGGER.error("分页查询Docker镜像列表失败", e);
            throw new BusinessException("查询镜像列表失败：" + e.getMessage());
        }
    }

    @Override
    @Cacheable(key = "#id", unless = "#result == null")
    public DockerImageForm getById(Long id) {
        try {
            LOGGER.info("根据ID查询Docker镜像详情，ID：{}", id);

            if (id == null) {
                throw new BusinessException("镜像ID不能为空");
            }

            // 使用MyBatis-Plus的getById方法
            DockerImageEntity entity = super.getById(id);
            if (entity == null) {
                LOGGER.warn("未找到ID为{}的Docker镜像", id);
                throw new BusinessException("镜像不存在");
            }

            // 使用MapStruct转换Entity到Form
            DockerImageForm result = swarmEntityMapper.toDockerImageForm(entity);

            LOGGER.info("查询到Docker镜像：{}", result.getName());
            return result;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("根据ID查询Docker镜像详情失败，ID：{}", id, e);
            throw new BusinessException("查询镜像详情失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CachePut(key = "#result.id")
    public DockerImageForm save(DockerImageForm form) {
        try {
            LOGGER.info("保存Docker镜像信息：{}", form);

            // 验证表单数据
            if (!form.isValid()) {
                throw new BusinessException(form.getValidationError());
            }

            // 检查镜像名称和标签是否已存在
            if (checkImageExists(form.getName(), form.getTag())) {
                throw new BusinessException("镜像名称和标签组合已存在");
            }

            // 使用MapStruct转换为实体对象
            DockerImageEntity entity = swarmEntityMapper.toDockerImageEntity(form);
            entity.setCreateTime(LocalDateTime.now());
            entity.setUpdateTime(LocalDateTime.now());

            // 设置默认值
            if (StringUtils.isBlank(entity.getTag())) {
                entity.setTag("latest");
            }
            if (StringUtils.isBlank(entity.getArchitecture())) {
                entity.setArchitecture("amd64");
            }
            if (StringUtils.isBlank(entity.getOs())) {
                entity.setOs("linux");
            }
            if (StringUtils.isBlank(entity.getStatus())) {
                entity.setStatus("active");
            }

            // 保存到数据库
            boolean success = this.save(entity);
            if (!success) {
                throw new BusinessException("保存镜像信息失败");
            }

            // 使用MapStruct转换返回数据
            DockerImageForm result = swarmEntityMapper.toDockerImageForm(entity);
            LOGGER.info("Docker镜像保存成功，ID：{}", result.getId());
            return result;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("保存Docker镜像信息失败", e);
            throw new BusinessException("保存镜像信息失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CachePut(key = "#form.id")
    public DockerImageForm update(DockerImageForm form) {
        try {
            LOGGER.info("更新Docker镜像信息：{}", form);

            if (form.getId() == null) {
                throw new BusinessException("镜像ID不能为空");
            }

            // 验证表单数据
            if (!form.isValid()) {
                throw new BusinessException(form.getValidationError());
            }

            // 检查镜像是否存在
            DockerImageEntity existingEntity = super.getById(form.getId());
            if (existingEntity == null) {
                throw new BusinessException("镜像不存在");
            }

            // 检查名称和标签组合是否与其他镜像冲突
            DockerImageEntity conflictEntity = dockerImageRepository.queryImageByNameAndTag(form.getName(), form.getTag());
            if (conflictEntity != null && !conflictEntity.getId().equals(form.getId())) {
                throw new BusinessException("镜像名称和标签组合已存在");
            }

            // 使用MapStruct转换实体对象
            DockerImageEntity entity = swarmEntityMapper.toDockerImageEntity(form);
            entity.setUpdateTime(LocalDateTime.now());
            entity.setCreateTime(existingEntity.getCreateTime()); // 保持原创建时间

            // 更新到数据库
            boolean success = this.updateById(entity);
            if (!success) {
                throw new BusinessException("更新镜像信息失败");
            }

            // 使用MapStruct转换返回更新后的数据
            DockerImageForm result = swarmEntityMapper.toDockerImageForm(entity);
            LOGGER.info("Docker镜像更新成功，ID：{}", result.getId());
            return result;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("更新Docker镜像信息失败", e);
            throw new BusinessException("更新镜像信息失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(key = "#id")
    public boolean delete(Long id) {
        try {
            LOGGER.info("删除Docker镜像，ID：{}", id);

            if (id == null) {
                throw new BusinessException("镜像ID不能为空");
            }

            // 检查镜像是否存在
            DockerImageEntity entity = super.getById(id);
            if (entity == null) {
                throw new BusinessException("镜像不存在");
            }

            // 检查镜像是否被容器使用
            if (isImageInUse(id)) {
                int containerCount = getContainerCountByImageId(id);
                throw new BusinessException("镜像正在被" + containerCount + "个容器使用，无法删除");
            }

            // 删除镜像
            boolean success = this.removeById(id);
            if (!success) {
                throw new BusinessException("删除镜像失败");
            }

            LOGGER.info("Docker镜像删除成功，ID：{}", id);
            return true;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("删除Docker镜像失败，ID：{}", id, e);
            throw new BusinessException("删除镜像失败：" + e.getMessage());
        }
    }

    @Override
    @Cacheable(key = "'available-images'")
    public List<DockerImageForm> getAvailableImages() {
        try {
            LOGGER.info("获取可用Docker镜像列表");

            // 使用MyBatis-Plus Lambda查询
            LambdaQueryWrapper<DockerImageEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DockerImageEntity::getStatus, "active")
                    .orderByAsc(DockerImageEntity::getName)
                    .orderByAsc(DockerImageEntity::getTag);

            List<DockerImageEntity> entities = this.list(wrapper);
            List<DockerImageForm> result = swarmEntityMapper.toDockerImageFormList(entities);

            LOGGER.info("查询到{}个可用Docker镜像", result.size());
            return result;

        } catch (Exception e) {
            LOGGER.error("获取可用Docker镜像列表失败", e);
            throw new BusinessException("获取可用镜像列表失败：" + e.getMessage());
        }
    }

    @Override
    @CacheEvict(key = "'available-images'")
    public boolean syncImagesFromRegistry() {
        try {
            LOGGER.info("开始从Docker同步镜像信息");

            // 获取Docker客户端
            DockerClient dockerClient = dockerSwarmServiceManager.getDockerClient();

            // 获取Docker中的所有镜像
            List<Image> dockerImages = dockerClient.listImagesCmd().exec();
            LOGGER.info("从Docker获取到{}个镜像", dockerImages.size());

            int syncedCount = 0;
            int updatedCount = 0;

            for (Image dockerImage : dockerImages) {
                // 跳过无标签的镜像
                if (dockerImage.getRepoTags() == null) {
                    continue;
                }

                for (String repoTag : dockerImage.getRepoTags()) {
                    // 解析镜像名称和标签
                    String[] parts = parseImageNameAndTag(repoTag);
                    String imageName = parts[0];
                    String imageTag = parts[1];
                    if (!imageName.contains(CCSP_CONSOLE)) {
                        continue;
                    }
                    // 检查数据库中是否已存在此镜像
                    DockerImageEntity existingEntity = dockerImageRepository.queryImageByNameAndTag(imageName, imageTag);

                    if (existingEntity == null) {
                        // 新增镜像记录
                        DockerImageEntity newEntity = createImageEntityFromDocker(dockerImage, imageName, imageTag);
                        this.save(newEntity);
                        syncedCount++;
                        LOGGER.debug("新增镜像记录: {}:{}", imageName, imageTag);
                    } else {
                        // 更新已存在的镜像信息
                        boolean updated = updateImageEntityFromDocker(existingEntity, dockerImage);
                        if (updated) {
                            this.updateById(existingEntity);
                            updatedCount++;
                            LOGGER.debug("更新镜像记录: {}:{}", imageName, imageTag);
                        }
                    }
                }
            }

            // 标记不在Docker中的镜像为不可用
            int inactiveCount = markInactiveImages(dockerImages);

            // 清理长期不活跃的镜像（超过30天）
            int removedCount = removeOldInactiveImages(30);

            LOGGER.info("Docker镜像信息同步完成 - 新增: {}, 更新: {}, 标记不可用: {}, 清理过期: {}",
                    syncedCount, updatedCount, inactiveCount, removedCount);
            return true;

        } catch (Exception e) {
            LOGGER.error("同步Docker镜像信息失败", e);
            throw new BusinessException("同步镜像信息失败：" + e.getMessage());
        }
    }

    @Override
    public boolean checkImageExists(String imageName, String tag) {
        try {
            if (StringUtils.isBlank(imageName) || StringUtils.isBlank(tag)) {
                return false;
            }

            // 首先检查数据库
            DockerImageEntity entity = dockerImageRepository.queryImageByNameAndTag(imageName, tag);
            if (entity != null && "active".equals(entity.getStatus())) {
                return true;
            }

            // 如果数据库中没有或状态不是active，则直接检查Docker
            try {
                DockerClient dockerClient = dockerSwarmServiceManager.getDockerClient();
                String fullImageName = imageName + ":" + tag;

                // 尝试检查镜像是否存在于Docker中
                List<Image> images = dockerClient.listImagesCmd()
                        .withImageNameFilter(fullImageName)
                        .exec();

                boolean existsInDocker = !images.isEmpty();

                // 如果Docker中存在但数据库中不存在，同步到数据库
                if (existsInDocker && entity == null) {
                    Image dockerImage = images.get(0);
                    DockerImageEntity newEntity = createImageEntityFromDocker(dockerImage, imageName, tag);
                    this.save(newEntity);
                    LOGGER.info("自动同步镜像到数据库: {}:{}", imageName, tag);
                }

                return existsInDocker;

            } catch (Exception dockerEx) {
                LOGGER.warn("检查Docker镜像失败，回退到数据库查询: {}:{}, 错误: {}",
                        imageName, tag, dockerEx.getMessage());
                return entity != null;
            }

        } catch (Exception e) {
            LOGGER.error("检查镜像是否存在失败，镜像名称：{}，标签：{}", imageName, tag, e);
            return false;
        }
    }

    @Override
    public boolean isImageInUse(Long imageId) {
        try {
            if (imageId == null) {
                return false;
            }

            int containerCount = containerInstanceRepository.countContainersByImageId(imageId);
            return containerCount > 0;

        } catch (Exception e) {
            LOGGER.error("检查镜像是否被使用失败，镜像ID：{}", imageId, e);
            return false;
        }
    }

    @Override
    public int getContainerCountByImageId(Long imageId) {
        try {
            if (imageId == null) {
                return 0;
            }

            return containerInstanceRepository.countContainersByImageId(imageId);

        } catch (Exception e) {
            LOGGER.error("获取镜像使用的容器数量失败，镜像ID：{}", imageId, e);
            return 0;
        }
    }

    @Override
    public List<DockerImageForm> getImagesByStatus(String status) {
        try {
            LOGGER.info("根据状态查询Docker镜像列表，状态：{}", status);

            if (StringUtils.isBlank(status)) {
                throw new BusinessException("状态不能为空");
            }

            // 使用MyBatis-Plus Lambda查询
            LambdaQueryWrapper<DockerImageEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DockerImageEntity::getStatus, status)
                    .orderByDesc(DockerImageEntity::getCreateTime);

            List<DockerImageEntity> entities = this.list(wrapper);
            List<DockerImageForm> result = swarmEntityMapper.toDockerImageFormList(entities);

            LOGGER.info("查询到{}个状态为{}的Docker镜像", result.size(), status);
            return result;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("根据状态查询Docker镜像列表失败，状态：{}", status, e);
            throw new BusinessException("查询镜像列表失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public boolean batchUpdateStatus(List<Long> imageIds, String status) {
        try {
            LOGGER.info("批量更新Docker镜像状态，镜像数量：{}，新状态：{}", imageIds.size(), status);

            if (imageIds == null || imageIds.isEmpty()) {
                throw new BusinessException("镜像ID列表不能为空");
            }

            if (StringUtils.isBlank(status)) {
                throw new BusinessException("状态不能为空");
            }

            // 使用MyBatis-Plus Lambda更新
            LambdaUpdateWrapper<DockerImageEntity> wrapper = new LambdaUpdateWrapper<>();
            wrapper.in(DockerImageEntity::getId, imageIds)
                    .set(DockerImageEntity::getStatus, status)
                    .set(DockerImageEntity::getUpdateTime, LocalDateTime.now());

            boolean success = this.update(wrapper);
            if (!success) {
                throw new BusinessException("批量更新镜像状态失败");
            }

            LOGGER.info("批量更新Docker镜像状态成功");
            return true;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("批量更新Docker镜像状态失败", e);
            throw new BusinessException("批量更新镜像状态失败：" + e.getMessage());
        }
    }

    @Override
    @CacheEvict(key = "#imageId != null ? #imageId.toString() : 'all'", condition = "#imageId != null", allEntries = true)
    public void clearImageCache(Long imageId) {
        if (imageId != null) {
            LOGGER.info("清除Docker镜像缓存，镜像ID：{}", imageId);
        } else {
            LOGGER.info("清除所有Docker镜像缓存");
        }
    }

    /**
     * 解析镜像名称和标签
     *
     * @param repoTag 完整的镜像名称，如 nginx:latest
     * @return [0]为镜像名，[1]为标签
     */
    private String[] parseImageNameAndTag(String repoTag) {
        if (StringUtils.isBlank(repoTag)) {
            return new String[]{"", "latest"};
        }

        // 处理 <none>:<none> 类型的镜像
        if ("<none>:<none>".equals(repoTag) || repoTag.startsWith("<none>")) {
            return new String[]{"", "latest"};
        }

        int lastColonIndex = repoTag.lastIndexOf(':');
        if (lastColonIndex > 0) {
            String imageName = repoTag.substring(0, lastColonIndex);
            String tag = repoTag.substring(lastColonIndex + 1);
            return new String[]{imageName, tag};
        } else {
            return new String[]{repoTag, "latest"};
        }
    }

    /**
     * 从Docker镜像创建数据库实体
     */
    private DockerImageEntity createImageEntityFromDocker(Image dockerImage, String imageName, String imageTag) {
        DockerImageEntity entity = new DockerImageEntity();
        entity.setName(imageName);
        entity.setTag(imageTag);
        entity.setStatus("active");

        // 转换镜像大小从字节到MB
        if (dockerImage.getSize() != null) {
            entity.setSizeMb(dockerImage.getSize() / (1024 * 1024));
        }

        entity.setArchitecture("amd64"); // 默认架构
        entity.setOs("linux"); // 默认操作系统

        LocalDateTime now = LocalDateTime.now();
        entity.setCreateTime(now);
        entity.setUpdateTime(now);

        // 设置仓库信息
        if (imageName.contains("/")) {
            String[] parts = imageName.split("/", 2);
            entity.setRepository(parts[0]);
        } else {
            entity.setRepository("docker.io"); // 默认Docker Hub
        }

        return entity;
    }

    /**
     * 从Docker镜像更新数据库实体
     */
    private boolean updateImageEntityFromDocker(DockerImageEntity existingEntity, Image dockerImage) {
        boolean updated = false;

        // 更新镜像大小
        if (dockerImage.getSize() != null) {
            Long newSizeMb = dockerImage.getSize() / (1024 * 1024);
            if (!newSizeMb.equals(existingEntity.getSizeMb())) {
                existingEntity.setSizeMb(newSizeMb);
                updated = true;
            }
        }

        // 更新状态为active（如果之前不是）
        if (!"active".equals(existingEntity.getStatus())) {
            existingEntity.setStatus("active");
            updated = true;
        }

        if (updated) {
            existingEntity.setUpdateTime(LocalDateTime.now());
        }

        return updated;
    }

    /**
     * 标记不在Docker中的镜像为不可用
     */
    private int markInactiveImages(List<Image> dockerImages) {
        try {
            // 收集Docker中所有的镜像名:tag组合
            Set<String> dockerImageTags = new HashSet<>();
            for (Image dockerImage : dockerImages) {
                if (dockerImage.getRepoTags() != null) {
                    for (String repoTag : dockerImage.getRepoTags()) {
                        if (!"<none>:<none>".equals(repoTag) && !repoTag.startsWith("<none>")) {
                            dockerImageTags.add(repoTag);
                        }
                    }
                }
            }

            // 查找数据库中状态为active但不在Docker中的镜像
            LambdaQueryWrapper<DockerImageEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DockerImageEntity::getStatus, "active");

            List<DockerImageEntity> activeImages = this.list(wrapper);
            List<DockerImageEntity> toInactive = new ArrayList<>();

            for (DockerImageEntity entity : activeImages) {
                String fullName = entity.getName() + ":" + entity.getTag();
                if (!dockerImageTags.contains(fullName)) {
                    entity.setStatus("inactive");
                    entity.setUpdateTime(LocalDateTime.now());
                    toInactive.add(entity);
                }
            }

            // 批量更新
            if (!toInactive.isEmpty()) {
                this.updateBatchById(toInactive);
            }

            return toInactive.size();

        } catch (Exception e) {
            LOGGER.error("标记不活跃镜像失败", e);
            return 0;
        }
    }

    /**
     * 清理长期不活跃的镜像
     *
     * @param inactiveDays 不活跃天数阈值
     * @return 清理的镜像数量
     */
    private int removeOldInactiveImages(int inactiveDays) {
        try {
            LOGGER.info("开始清理超过{}天的不活跃镜像", inactiveDays);

            // 计算阈值时间
            LocalDateTime thresholdTime = LocalDateTime.now().minusDays(inactiveDays);

            // 查找长期不活跃的镜像
            LambdaQueryWrapper<DockerImageEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DockerImageEntity::getStatus, "inactive")
                    .lt(DockerImageEntity::getUpdateTime, thresholdTime);

            List<DockerImageEntity> oldInactiveImages = this.list(wrapper);

            if (!oldInactiveImages.isEmpty()) {
                // 检查这些镜像是否被容器使用
                List<DockerImageEntity> safeToRemove = new ArrayList<>();
                for (DockerImageEntity image : oldInactiveImages) {
                    if (!isImageInUse(image.getId())) {
                        safeToRemove.add(image);
                    }
                }

                if (!safeToRemove.isEmpty()) {
                    // 删除安全的镜像
                    List<Long> imageIds = safeToRemove.stream()
                            .map(DockerImageEntity::getId)
                            .collect(java.util.stream.Collectors.toList());

                    LambdaQueryWrapper<DockerImageEntity> deleteWrapper = new LambdaQueryWrapper<>();
                    deleteWrapper.in(DockerImageEntity::getId, imageIds);
                    int removedCount = dockerImageRepository.delete(deleteWrapper);

                    LOGGER.info("清理长期不活跃镜像完成，清理数量: {}", removedCount);
                    return removedCount;
                }
            }

            return 0;

        } catch (Exception e) {
            LOGGER.error("清理长期不活跃镜像失败", e);
            return 0;
        }
    }
}